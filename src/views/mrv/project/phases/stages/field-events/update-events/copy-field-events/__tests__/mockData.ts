import { BULK_COPY_FIELD_EVENTS } from "views/mrv/graphql/mutations/field-events";
import { mockFieldId } from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import { CopyFieldEventsMode } from "__generated__/gql/graphql";

// Mock context values that match the test setup
export const MOCK_PROJECT_ID = 1;
export const MOCK_PHASE_ID = 6190;
export const MOCK_STAGE_ID = 14989;

export const mockSelectedFieldEvents = {
  field: {id: '108264', name: 'Test Field'},
  events: {
    'cultivation-cycle-1': ['event-1', 'event-2'],
    'cultivation-cycle-2': ['event-3'],
  },
};

// Helper function to create mock events for given field IDs
export const createMockEvents = (targetFieldIds: Array<string>) => {
  const allEventIds = Object.values(mockSelectedFieldEvents.events).flat();

  return targetFieldIds.flatMap(targetFieldId =>
    allEventIds.map(sourceEventId => ({
      sourceEventId: String(sourceEventId),
      targetFieldId: parseInt(targetFieldId, 10),
      targetProjectId: MOCK_PROJECT_ID,
      targetPhaseId: MOCK_PHASE_ID,
      targetStageId: MOCK_STAGE_ID,
    }))
  );
};

// Helper function to create Apollo mock for BULK_COPY_FIELD_EVENTS
export const createBulkCopyFieldEventsMock = (
  targetFieldIds: Array<string>,
  copyMode: CopyFieldEventsMode,
  isError = false
) => {
  const events = createMockEvents(targetFieldIds);

  if (isError) {
    return {
      request: {
        query: BULK_COPY_FIELD_EVENTS,
        variables: { events, copyMode },
      },
      error: new Error('Failed to copy field events'),
    };
  }

  return {
    request: {
      query: BULK_COPY_FIELD_EVENTS,
      variables: { events, copyMode },
    },
    result: {
      data: {
        bulkCopyFieldEvents: events.map((_, index) => ({
          id: `new-event-${index + 1}`,
          type: 'IrrigationEvent',
        })),
      },
    },
  };
};

// Pre-configured mocks for common test scenarios
export const BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK = createBulkCopyFieldEventsMock(
  [mockFieldId],
  CopyFieldEventsMode.Copy
);

export const BULK_COPY_FIELD_EVENTS_OVERWRITE_MOCK = createBulkCopyFieldEventsMock(
  [mockFieldId],
  CopyFieldEventsMode.Overwrite
);

export const BULK_COPY_FIELD_EVENTS_ERROR_MOCK = createBulkCopyFieldEventsMock(
  [mockFieldId],
  CopyFieldEventsMode.Overwrite,
  true
);